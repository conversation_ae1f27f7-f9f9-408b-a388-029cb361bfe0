"""
EEG源定位系统 - 数据管理模块

该模块负责管理EEG和MRI数据的加载、预处理和格式转换。
支持多种数据格式，提供统一的数据接口。

主要功能：
1. EEG数据加载和预处理
2. MRI数据处理和格式转换
3. 数据质量检查和验证
4. 坐标系统转换和配准
"""

import os
import numpy as np
import pandas as pd
import nibabel as nib
import mne
from typing import Dict, List, Optional, Tuple, Union
import logging
import yaml
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DataManager:
    """数据管理器主类"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        初始化数据管理器
        
        参数:
            config_path: 配置文件路径
        """
        self.config = self._load_config(config_path)
        self.eeg_loader = EEGLoader(self.config)
        self.mri_processor = MRIProcessor(self.config)
        self.data_converter = DataConverter(self.config)
        
        # 创建必要的目录
        self._create_directories()
        
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            logger.info(f"成功加载配置文件: {config_path}")
            return config
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            raise
            
    def _create_directories(self):
        """创建必要的目录结构"""
        dirs = [
            self.config['data_paths']['output_dir'],
            self.config['data_paths']['cache_dir'],
            self.config['data_paths']['template_dir']
        ]
        
        for dir_path in dirs:
            Path(dir_path).mkdir(parents=True, exist_ok=True)
            logger.info(f"创建目录: {dir_path}")


class EEGLoader:
    """EEG数据加载器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.eeg_config = config['eeg_processing']
        
    def load_eeg_data(self, subject_id: str, data_type: str = "guinea_bissau") -> mne.io.Raw:
        """
        加载EEG数据
        
        参数:
            subject_id: 被试ID
            data_type: 数据类型 ("guinea_bissau" 或 "nigeria")
            
        返回:
            mne.io.Raw: MNE原始数据对象
        """
        try:
            if data_type == "guinea_bissau":
                data_dir = Path(self.config['data_paths']['eeg_data_dir']) / "EEGs_Guinea-Bissau"
                file_pattern = f"signal-{subject_id}.csv.gz"
            else:
                data_dir = Path(self.config['data_paths']['eeg_data_dir']) / "EEGs_Nigeria"
                file_pattern = f"signal-{subject_id}-1.csv.gz"
                
            file_path = data_dir / file_pattern
            
            if not file_path.exists():
                raise FileNotFoundError(f"EEG数据文件不存在: {file_path}")
                
            # 加载CSV格式的EEG数据
            eeg_data = pd.read_csv(file_path, compression='gzip')
            
            # 转换为MNE格式
            raw = self._convert_to_mne_raw(eeg_data, subject_id)
            
            logger.info(f"成功加载EEG数据: {file_path}")
            return raw
            
        except Exception as e:
            logger.error(f"加载EEG数据失败: {e}")
            raise
            
    def _convert_to_mne_raw(self, eeg_data: pd.DataFrame, subject_id: str) -> mne.io.Raw:
        """将pandas DataFrame转换为MNE Raw对象"""
        
        # 假设数据格式：第一列为时间，其余列为通道数据
        if 'time' in eeg_data.columns:
            time_col = 'time'
            data_cols = [col for col in eeg_data.columns if col != 'time']
        else:
            # 如果没有时间列，使用索引作为时间
            data_cols = list(eeg_data.columns)
            
        # 提取通道数据 (通道 x 时间点)
        data = eeg_data[data_cols].T.values
        
        # 创建通道信息
        ch_names = data_cols
        ch_types = ['eeg'] * len(ch_names)
        
        # 创建Info对象
        sfreq = self.eeg_config['sampling_rate']
        info = mne.create_info(ch_names=ch_names, sfreq=sfreq, ch_types=ch_types)
        
        # 创建Raw对象
        raw = mne.io.RawArray(data, info)
        
        # 设置标准电极位置（如果可用）
        try:
            montage = mne.channels.make_standard_montage('standard_1020')
            raw.set_montage(montage, match_case=False, on_missing='ignore')
        except Exception as e:
            logger.warning(f"设置电极位置失败: {e}")
            
        return raw
        
    def preprocess_eeg(self, raw: mne.io.Raw) -> mne.io.Raw:
        """
        EEG数据预处理
        
        参数:
            raw: 原始EEG数据
            
        返回:
            mne.io.Raw: 预处理后的EEG数据
        """
        try:
            # 复制数据以避免修改原始数据
            raw_processed = raw.copy()
            
            # 滤波配置
            filter_config = self.eeg_config['filtering']
            
            # 高通滤波
            if filter_config['highpass'] > 0:
                raw_processed.filter(
                    l_freq=filter_config['highpass'],
                    h_freq=None,
                    fir_design='firwin'
                )
                logger.info(f"应用高通滤波: {filter_config['highpass']} Hz")
                
            # 低通滤波
            if filter_config['lowpass'] > 0:
                raw_processed.filter(
                    l_freq=None,
                    h_freq=filter_config['lowpass'],
                    fir_design='firwin'
                )
                logger.info(f"应用低通滤波: {filter_config['lowpass']} Hz")
                
            # 工频滤波
            if filter_config['notch'] > 0:
                raw_processed.notch_filter(
                    freqs=filter_config['notch'],
                    fir_design='firwin'
                )
                logger.info(f"应用工频滤波: {filter_config['notch']} Hz")
                
            # 预处理步骤
            preproc_config = self.eeg_config['preprocessing']
            
            # 去除直流分量
            if preproc_config['remove_dc']:
                raw_processed._data = raw_processed._data - np.mean(raw_processed._data, axis=1, keepdims=True)
                logger.info("去除直流分量")
                
            # 去趋势
            if preproc_config['detrend']:
                from scipy import signal
                for i in range(raw_processed._data.shape[0]):
                    raw_processed._data[i] = signal.detrend(raw_processed._data[i])
                logger.info("应用去趋势处理")
                
            return raw_processed
            
        except Exception as e:
            logger.error(f"EEG预处理失败: {e}")
            raise


class MRIProcessor:
    """MRI数据处理器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.head_config = config['head_modeling']
        
    def load_mri_data(self, subject_id: str) -> Dict[str, nib.Nifti1Image]:
        """
        加载MRI数据
        
        参数:
            subject_id: 被试ID
            
        返回:
            Dict: 包含不同MRI数据的字典
        """
        try:
            mri_data = {}
            data_dir = Path(self.config['data_paths']['mri_data_dir']) / subject_id
            
            # 加载原始mask数据
            mask_orig_path = data_dir / f"{subject_id}_MaskInOrig.nii.gz"
            if mask_orig_path.exists():
                mri_data['mask_orig'] = nib.load(str(mask_orig_path))
                logger.info(f"加载原始mask: {mask_orig_path}")
                
            # 加载原始数据mask
            mask_raw_path = data_dir / f"{subject_id}_MaskInRawData.nii.gz"
            if mask_raw_path.exists():
                mri_data['mask_raw'] = nib.load(str(mask_raw_path))
                logger.info(f"加载原始数据mask: {mask_raw_path}")
                
            # 检查是否有T1数据
            t1_path = Path(f"sub-{subject_id}_T1w.nii")
            if t1_path.exists():
                mri_data['t1'] = nib.load(str(t1_path))
                logger.info(f"加载T1数据: {t1_path}")
                
            if not mri_data:
                raise FileNotFoundError(f"未找到被试 {subject_id} 的MRI数据")
                
            return mri_data
            
        except Exception as e:
            logger.error(f"加载MRI数据失败: {e}")
            raise
            
    def validate_mri_data(self, mri_data: Dict[str, nib.Nifti1Image]) -> bool:
        """
        验证MRI数据质量
        
        参数:
            mri_data: MRI数据字典
            
        返回:
            bool: 验证是否通过
        """
        try:
            validation_results = []
            
            for data_type, img in mri_data.items():
                # 检查数据维度
                if len(img.shape) != 3:
                    logger.warning(f"{data_type} 数据维度异常: {img.shape}")
                    validation_results.append(False)
                    continue
                    
                # 检查体素尺寸
                voxel_size = img.header.get_zooms()[:3]
                if any(size > 2.0 for size in voxel_size):  # 体素尺寸不应超过2mm
                    logger.warning(f"{data_type} 体素尺寸过大: {voxel_size}")
                    
                # 检查数据范围
                data = img.get_fdata()
                if np.all(data == 0):
                    logger.warning(f"{data_type} 数据全为零")
                    validation_results.append(False)
                    continue
                    
                validation_results.append(True)
                logger.info(f"{data_type} 数据验证通过")
                
            return all(validation_results)
            
        except Exception as e:
            logger.error(f"MRI数据验证失败: {e}")
            return False


class DataConverter:
    """数据格式转换器"""
    
    def __init__(self, config: Dict):
        self.config = config
        
    def convert_coordinates(self, coords: np.ndarray, 
                          from_space: str, to_space: str) -> np.ndarray:
        """
        坐标系统转换
        
        参数:
            coords: 坐标数组 (N x 3)
            from_space: 源坐标系统
            to_space: 目标坐标系统
            
        返回:
            np.ndarray: 转换后的坐标
        """
        # 这里实现坐标转换逻辑
        # 实际实现需要根据具体的坐标系统进行
        logger.info(f"坐标转换: {from_space} -> {to_space}")
        return coords
        
    def save_results(self, data: Dict, output_path: str, format_type: str = "nifti"):
        """
        保存分析结果
        
        参数:
            data: 要保存的数据
            output_path: 输出路径
            format_type: 保存格式
        """
        try:
            output_path = Path(output_path)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            if format_type == "nifti":
                # 保存为NIfTI格式
                if isinstance(data, dict) and 'image' in data:
                    nib.save(data['image'], str(output_path))
                    
            elif format_type == "json":
                import json
                with open(output_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                    
            logger.info(f"结果已保存: {output_path}")
            
        except Exception as e:
            logger.error(f"保存结果失败: {e}")
            raise
